import { Button, Input, Tooltip } from "@nextui-org/react";
import { useEffect, useRef, useState } from "react";

// 使用内联SVG图标代替导入
const CopyIcon = () => (
  <svg
    fill="none"
    height="16"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect height="13" rx="2" ry="2" width="13" x="9" y="9" />
    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
  </svg>
);

const RefreshIcon = () => (
  <svg
    fill="none"
    height="16"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23 4v6h-6" />
    <path d="M1 20v-6h6" />
    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15" />
  </svg>
);

/**
 * 聊天机器人组件
 * @returns {JSX.Element} - 聊天界面
 */
const Chatbot = () => {
  // 消息列表状态
  const [messages, setMessages] = useState([
    {
      role: "assistant",
      content:
        "您好！我是智能助手，很高兴为您服务。请问有什么我可以帮助您的吗？",
    },
  ]);

  // 输入框文本状态
  const [inputText, setInputText] = useState("");

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 当前请求控制器
  const [currentController, setCurrentController] = useState(null);

  // 滚动定位引用
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  /**
   * 调用API获取回答
   * @param {string} question - 用户问题
   */
  const callStreamAPI = async (question: string) => {
    setIsLoading(true);

    // 创建AbortController用于取消请求
    const controller = new AbortController();

    setCurrentController(controller);

    // 添加空的AI消息占位
    setMessages((prev) => [...prev, { role: "assistant", content: "" }]);

    try {
      // API调用
      const response = await fetch(
        "https://api.bigmodel.org/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization:
              "Bearer sk-cmqi8iWV0aS8oNE2OR71OhiWwTUtXV9BsNevqaua2Bdd27NV",
          },
          body: JSON.stringify({
            model: "o1-mini",
            messages: [{ role: "user", content: question }],
            stream: true,
          }),
          signal: controller.signal,
        },
      );

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (response.body) {
        const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let partialContent = "";

      while (true) {
        if (controller.signal.aborted) break;

        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.trim().startsWith("data: ")) {
            try {
              const data = JSON.parse(line.trim().substring(6));

              if (data.choices && data.choices[0].delta.content) {
                partialContent += data.choices[0].delta.content;

                setMessages((prev) => {
                  const newMessages = [...prev];

                  newMessages[newMessages.length - 1].content = partialContent;

                  return newMessages;
                });
              }
            } catch (e) {
              console.error("解析响应失败:", e);
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== "AbortError") {
        // 显示错误消息
        setMessages((prev) => {
          const newMessages = [...prev];
          
          // 确保数组不为空且最后一条是AI消息
          if (newMessages.length > 0) {
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage.role === "assistant") {
              lastMessage.content = "请求失败，请重试";
            }
          }
          
          return newMessages;
        });
      }
    } finally {
      setIsLoading(false);
      setCurrentController(null);
    }
  };

  /**
   * 处理发送消息
   * @param {React.FormEvent<HTMLFormElement>} e - 表单提交事件
   */
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!inputText.trim() || isLoading) return;

    // 添加用户消息
    setMessages((prev) => [
      ...prev,
      { role: "user", content: inputText.trim() },
    ]);

    // 调用API
    callStreamAPI(inputText.trim());

    // 清空输入框
    setInputText("");
  };

  /**
   * 处理重试请求
   */
  const handleRetry = () => {
    // 取消当前请求（如果存在）
    if (currentController) {
      currentController.abort();
    }

    // 获取最后一条用户消息
    const lastUserMessageIndex = [...messages]
      .reverse()
      .findIndex((msg) => msg.role === "user");

    if (lastUserMessageIndex === -1) return;

    const lastUserMessage =
      messages[messages.length - 1 - lastUserMessageIndex];

    // 删除最后一条AI消息
    setMessages((prev) => prev.slice(0, -1));

    // 重新调用API
    callStreamAPI(lastUserMessage.content);
  };

  /**
   * 处理复制消息内容
   * @param {string} text - 要复制的文本
   */
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="w-full h-screen flex flex-col">
      {/* 顶部智能助手栏 */}
      <div className="w-full bg-gradient-to-r from-blue-600 to-blue-800 py-4 px-6 shadow-lg">
        <h1 className="text-2xl font-bold text-center text-white drop-shadow-sm">
          智能助手
        </h1>
      </div>

      {/* 中间消息区域 */}
      <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
        <div className="flex flex-col gap-6 max-w-4xl mx-auto">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`relative group p-4 rounded-2xl max-w-[80%] shadow-sm transition-all duration-300 ${
                msg.role === "user"
                  ? "self-end bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto"
                  : "self-start bg-white border border-gray-100 shadow-md mr-auto"
                }`}
            >
              <div className="whitespace-pre-wrap text-gray-700 font-normal">
                {msg.content || "思考中..."}
              </div>

              {/* 悬停按钮 */}
              <div
                className={`absolute top-2 ${msg.role === "user" ? "left-2" : "right-2"} flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
              >
                {msg.role === "assistant" && msg.content && (
                  <Tooltip content="重试">
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      onPress={handleRetry}
                    >
                      <RefreshIcon />
                    </Button>
                  </Tooltip>
                )}

                {msg.content && (
                  <Tooltip content="复制">
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      onPress={() => handleCopy(msg.content)}
                    >
                      <CopyIcon />
                    </Button>
                  </Tooltip>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* 底部输入区域 */}
      <div className="w-full border-t border-gray-200 bg-white p-6 shadow-lg">
        <form
          className="w-full max-w-4xl mx-auto flex gap-4"
          onSubmit={handleSubmit}
        >
          <Input
            fullWidth
            className="h-[50px] rounded-xl shadow-sm bg-white"
            disabled={isLoading}
            placeholder="请输入问题..."
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                handleSubmit(e);
                e.preventDefault();
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                handleSubmit(e);
                e.preventDefault();
              }
            }}
          />
          <Button
            className="h-[50px] px-6 rounded-xl shadow-md font-semibold bg-gradient-to-r from-blue-500 to-purple-600"
            color="primary"
            isLoading={isLoading}
            type="submit"
          >
            发送
          </Button>
        </form>
      </div>
    </div>
  );
};

export default Chatbot;
