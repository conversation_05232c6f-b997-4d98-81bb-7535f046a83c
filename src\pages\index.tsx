import { Button, Input, Tooltip } from "@heroui/react";
import { useEffect, useRef, useState } from "react";

import DefaultLayout from "@/layouts/default";

// 使用内联SVG图标代替导入
const CopyIcon = () => (
  <svg
    fill="none"
    height="16"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect height="13" rx="2" ry="2" width="13" x="9" y="9" />
    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
  </svg>
);

const RefreshIcon = () => (
  <svg
    fill="none"
    height="16"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23 4v6h-6" />
    <path d="M1 20v-6h6" />
    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15" />
  </svg>
);

export default function IndexPage() {
  // 消息列表状态
  const [messages, setMessages] = useState([
    {
      role: "assistant",
      content:
        "您好！我是智能助手，很高兴为您服务。请问有什么我可以帮助您的吗？",
    },
  ]);

  // 输入框文本状态
  const [inputText, setInputText] = useState("");

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 当前请求控制器
  const [currentController, setCurrentController] =
    useState<AbortController | null>(null);

  // 滚动定位引用
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  /**
   * 调用API获取回答
   * @param {string} question - 用户问题
   */
  const callStreamAPI = async (question: string) => {
    setIsLoading(true);

    // 创建AbortController用于取消请求
    const controller = new AbortController();

    setCurrentController(controller);

    // 添加空的AI消息占位
    setMessages((prev) => [...prev, { role: "assistant", content: "" }]);

    try {
      // API调用
      const response = await fetch(
        "https://api.bigmodel.org/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization:
              "Bearer sk-cmqi8iWV0aS8oNE2OR71OhiWwTUtXV9BsNevqaua2Bdd27NV",
          },
          body: JSON.stringify({
            model: "o1-mini",
            messages: [{ role: "user", content: question }],
            stream: true,
          }),
          signal: controller.signal,
        },
      );

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialContent = "";

        while (true) {
          if (controller.signal.aborted) break;

          const { done, value } = await reader.read();

          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.trim().startsWith("data: ")) {
              try {
                const data = JSON.parse(line.trim().substring(6));

                if (data.choices && data.choices[0].delta.content) {
                  partialContent += data.choices[0].delta.content;

                  setMessages((prev) => {
                    const newMessages = [...prev];

                    newMessages[newMessages.length - 1].content =
                      partialContent;

                    return newMessages;
                  });
                }
              } catch (e) {
                console.error("解析响应失败:", e);
              }
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== "AbortError") {
        // 显示错误消息
        setMessages((prev) => {
          const newMessages = [...prev];

          // 确保数组不为空且最后一条是AI消息
          if (newMessages.length > 0) {
            const lastMessage = newMessages[newMessages.length - 1];

            if (lastMessage.role === "assistant") {
              lastMessage.content = "请求失败，请重试";
            }
          }

          return newMessages;
        });
      }
    } finally {
      setIsLoading(false);
      setCurrentController(null);
    }
  };

  /**
   * 处理发送消息
   * @param {React.FormEvent<HTMLFormElement>} e - 表单提交事件
   */
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!inputText.trim() || isLoading) return;

    // 添加用户消息
    setMessages((prev) => [
      ...prev,
      { role: "user", content: inputText.trim() },
    ]);

    // 调用API
    callStreamAPI(inputText.trim());

    // 清空输入框
    setInputText("");
  };

  /**
   * 处理重试请求
   */
  const handleRetry = () => {
    // 取消当前请求（如果存在）
    if (currentController) {
      currentController.abort();
    }

    // 获取最后一条用户消息
    const lastUserMessageIndex = [...messages]
      .reverse()
      .findIndex((msg) => msg.role === "user");

    if (lastUserMessageIndex === -1) return;

    const lastUserMessage =
      messages[messages.length - 1 - lastUserMessageIndex];

    // 删除最后一条AI消息
    setMessages((prev) => prev.slice(0, -1));

    // 重新调用API
    callStreamAPI(lastUserMessage.content);
  };

  /**
   * 处理复制消息内容
   * @param {string} text - 要复制的文本
   */
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <DefaultLayout>
      <section className="flex flex-col items-center justify-center py-8 md:py-10">
        <div className="w-full max-w-4xl h-[600px] flex flex-col border border-gray-200 rounded-lg overflow-hidden">
          {/* 消息容器 */}
          <div className="flex-1 p-4 overflow-y-auto space-y-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`relative group flex ${
                  msg.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`relative p-3 rounded-lg max-w-[80%] ${
                    msg.role === "user"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  <div className="message-content">
                    {msg.content || "思考中..."}
                  </div>

                  {/* 悬停按钮 */}
                  <div
                    className={`absolute top-2 ${msg.role === "user" ? "left-2" : "right-2"} flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                  >
                    {msg.role === "assistant" && msg.content && (
                      <Tooltip content="重试">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          onPress={handleRetry}
                        >
                          <RefreshIcon />
                        </Button>
                      </Tooltip>
                    )}

                    {msg.content && (
                      <Tooltip content="复制">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          onPress={() => handleCopy(msg.content)}
                        >
                          <CopyIcon />
                        </Button>
                      </Tooltip>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="border-t p-4">
            <form className="flex space-x-2" onSubmit={handleSubmit}>
              <Input
                className="flex-1"
                disabled={isLoading}
                placeholder="输入消息..."
                value={inputText}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setInputText(e.target.value)
                }
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    handleSubmit(e as any);
                    e.preventDefault();
                  }
                }}
              />
              <Button color="primary" isLoading={isLoading} type="submit">
                发送
              </Button>
            </form>
          </div>
        </div>
      </section>
    </DefaultLayout>
  );
}
