import { Button } from "@heroui/react";

import DefaultLayout from "@/layouts/default";

export default function IndexPage() {
  return (
    <DefaultLayout>
      <section className="flex flex-col items-center justify-center py-8 md:py-10">
        <div className="w-full max-w-4xl h-[600px] flex flex-col border border-gray-200 rounded-lg overflow-hidden">
          {/* 消息容器 */}
          <div className="flex-1 p-4 overflow-y-auto space-y-4">
            {/* 示例消息 */}
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                Hello! How can I help you today?
              </div>
            </div>
            <div className="flex justify-end">
              <div className="bg-blue-500 text-white rounded-lg p-3 max-w-[80%]">
                这是一个示例回复
              </div>
            </div>
          </div>

          {/* 输入区域 */}
          <div className="border-t p-4">
            <div className="flex space-x-2">
              <input
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入消息..."
                type="text"
              />
              <Button color="primary">发送</Button>
            </div>
          </div>
        </div>
      </section>
    </DefaultLayout>
  );
}
